﻿# -*- coding: utf-8 -*-
import os
import subprocess
import sys
import json
import tempfile
import glob
import argparse
import re
from datetime import datetime
from pypinyin import pinyin, Style

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def load_prompt_content(prompt_value):
    """
    加载prompt内容
    - 如果prompt_value以.md结尾，则从batch_configs/prompt目录读取对应的md文件
    - 否则直接返回prompt_value作为字符串内容

    Args:
        prompt_value: prompt参数值，可能是字符串内容或md文件名

    Returns:
        str: prompt的实际内容，如果读取失败则返回None
    """
    if prompt_value is None:
        return None

    # 检查是否为md文件名
    if isinstance(prompt_value, str) and prompt_value.endswith('.md'):
        # 构建文件路径
        prompt_file_path = os.path.join("batch_configs", "prompt", prompt_value)

        # 检查文件是否存在
        if not os.path.exists(prompt_file_path):
            print(f"警告：指定的prompt文件不存在: {prompt_file_path}")
            return None

        try:
            # 读取md文件内容
            with open(prompt_file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
            print(f"已从文件加载prompt: {prompt_file_path}")
            return content
        except Exception as e:
            print(f"错误：无法读取prompt文件 {prompt_file_path}: {e}")
            return None
    else:
        # 直接返回字符串内容
        return prompt_value

def parse_command_line_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description='AI图像识别验证工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用示例:
  python main.py                           # 交互式模式
  python main.py --name 1.json            # 指定配置文件运行
  python main.py --name 2025-08-01_example.json  # 指定配置文件运行
  python main.py --rename                  # 重命名配置文件为数字格式
  python main.py --list                    # 列出所有可用的配置文件
        '''
    )

    parser.add_argument(
        '--name',
        type=str,
        help='指定要使用的batch_configs配置文件名（如：1.json 或 2025-08-01_example.json）'
    )

    parser.add_argument(
        '--rename',
        action='store_true',
        help='将batch_configs目录中的所有JSON文件重命名为数字格式（1.json, 2.json, ...）'
    )

    parser.add_argument(
        '--list',
        action='store_true',
        help='列出batch_configs目录中所有可用的配置文件'
    )

    return parser.parse_args()

def rename_batch_config_files():
    """将batch_configs目录中的JSON文件重命名为数字格式"""
    batch_configs_dir = "batch_configs"
    if not os.path.exists(batch_configs_dir):
        print(f"错误：{batch_configs_dir} 文件夹不存在")
        return False

    # 获取所有JSON文件（排除子目录）
    json_files = []
    for file in os.listdir(batch_configs_dir):
        file_path = os.path.join(batch_configs_dir, file)
        if os.path.isfile(file_path) and file.endswith('.json'):
            json_files.append(file)

    if not json_files:
        print(f"在 {batch_configs_dir} 中没有找到JSON文件")
        return False

    # 按文件名排序
    json_files.sort()

    print(f"找到 {len(json_files)} 个JSON文件，开始重命名...")

    # 重命名文件
    renamed_count = 0
    for i, old_filename in enumerate(json_files, 1):
        new_filename = f"{i}.json"
        old_path = os.path.join(batch_configs_dir, old_filename)
        new_path = os.path.join(batch_configs_dir, new_filename)

        # 如果新文件名已经存在且不是当前文件，跳过
        if os.path.exists(new_path) and old_filename != new_filename:
            print(f"  跳过 {old_filename} -> {new_filename} (目标文件已存在)")
            continue

        # 如果文件名已经是数字格式，跳过
        if old_filename == new_filename:
            print(f"  跳过 {old_filename} (已是数字格式)")
            continue

        try:
            os.rename(old_path, new_path)
            print(f"  ✓ {old_filename} -> {new_filename}")
            renamed_count += 1
        except Exception as e:
            print(f"  ✗ 重命名失败 {old_filename} -> {new_filename}: {e}")

    print(f"重命名完成，共重命名 {renamed_count} 个文件")
    return True

def get_input_mode_choice():
    """让用户选择输入模式：手动输入或batch_configs读取"""
    print("请选择输入模式：")
    print("1. 手动输入")
    print("2. batch_configs读取")

    while True:
        choice = input("请输入选择（1或2）：").strip()
        if choice in ['1', '2']:
            return choice
        else:
            print("输入无效，请输入1或2")

def apply_default_values(config):
    """为配置项应用默认值（题型字段除外）"""
    # 定义默认值
    defaults = {
        "处理模式": 1,  # 1=单阶段，2=双阶段不发图片，3=双阶段发图片
        "round2批改模式": 2,  # 默认使用JSON比对
        "模型ID": 1,
        "response_format": 1,  # 默认使用text格式，1=text，2=json_object
        "图像文件夹": 1,
        "像素增强": "n",
        "像素粘连": "n",
        "图像放大倍数": 1
        # 注意：灰度阀门不设置默认值，因为只有在batch_configs中明确指定时才使用
    }

    # 应用默认值，但不覆盖已存在的值
    for key, default_value in defaults.items():
        if key not in config:
            config[key] = default_value
            print(f"  应用默认值: {key} = {default_value}")

    # 检查题型字段是否存在
    if "题型" not in config:
        print(f"  警告：配置中缺少必需的'题型'字段")
        return False

    return True

def get_batch_config(config_filename=None):
    """
    获取batch_configs配置文件

    Args:
        config_filename: 指定的配置文件名，如果为None则获取按数字排序的最小文件

    Returns:
        dict: 配置数据，如果失败则返回None
    """
    batch_configs_dir = "batch_configs"
    if not os.path.exists(batch_configs_dir):
        print(f"错误：{batch_configs_dir} 文件夹不存在")
        return None

    if config_filename:
        # 指定了文件名
        config_file_path = os.path.join(batch_configs_dir, config_filename)
        if not os.path.exists(config_file_path):
            print(f"错误：指定的配置文件不存在: {config_file_path}")
            return None
        target_file = config_file_path
        print(f"使用指定的配置文件：{config_filename}")
    else:
        # 未指定文件名，按数字排序选择最小的
        json_files = []
        for file in os.listdir(batch_configs_dir):
            file_path = os.path.join(batch_configs_dir, file)
            if os.path.isfile(file_path) and file.endswith('.json'):
                json_files.append(file)

        if not json_files:
            print(f"错误：{batch_configs_dir} 文件夹中没有找到JSON文件")
            return None

        # 按数字排序（优先数字文件名，然后按字母排序）
        def sort_key(filename):
            # 提取文件名中的数字（如果是纯数字.json格式）
            name_without_ext = filename[:-5]  # 去掉.json
            if name_without_ext.isdigit():
                return (0, int(name_without_ext))  # 数字文件优先，按数字大小排序
            else:
                return (1, filename)  # 非数字文件按字母排序

        json_files.sort(key=sort_key)
        target_file = os.path.join(batch_configs_dir, json_files[0])
        print(f"自动选择配置文件：{json_files[0]}")

    try:
        with open(target_file, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        print(f"已加载配置文件：{target_file}")

        # 处理batch_configs中的每个配置项，应用默认值
        batch_configs = config_data.get("batch_configs", [])
        valid_configs = []

        for i, config in enumerate(batch_configs):
            print(f"\n处理第 {i+1} 个配置:")
            if apply_default_values(config):
                valid_configs.append(config)
                print(f"  ✓ 配置 {i+1} 验证通过")
            else:
                print(f"  ✗ 配置 {i+1} 验证失败，跳过此配置")

        # 更新配置数据
        config_data["batch_configs"] = valid_configs
        print(f"\n有效配置数量: {len(valid_configs)}/{len(batch_configs)}")

        return config_data
    except Exception as e:
        print(f"错误：无法读取配置文件 {target_file}: {e}")
        return None

def get_latest_batch_config():
    """获取batch_configs文件夹中时间最晚的JSON文件（兼容性函数）"""
    return get_batch_config()

def list_available_configs():
    """列出可用的配置文件"""
    batch_configs_dir = "batch_configs"
    if not os.path.exists(batch_configs_dir):
        print(f"错误：{batch_configs_dir} 文件夹不存在")
        return []

    json_files = []
    for file in os.listdir(batch_configs_dir):
        file_path = os.path.join(batch_configs_dir, file)
        if os.path.isfile(file_path) and file.endswith('.json'):
            json_files.append(file)

    # 按数字排序
    def sort_key(filename):
        name_without_ext = filename[:-5]  # 去掉.json
        if name_without_ext.isdigit():
            return (0, int(name_without_ext))
        else:
            return (1, filename)

    json_files.sort(key=sort_key)
    return json_files

def get_processing_mode_choice():
    """让用户选择处理模式"""
    print("请选择处理模式：")
    print("1. 单阶段处理（调用one_stage_test）")
    print("2. 双阶段不发图片（调用test+test2）")
    print("3. 双阶段发图片（调用test+test3）")

    while True:
        choice = input("请输入选择（1、2或3）：").strip()
        if choice in ['1', '2', '3']:
            return choice
        else:
            print("输入无效，请输入1、2或3")

def get_round2_grading_mode():
    """让用户选择round2批改模式"""
    print("请选择round2批改模式：")
    print("1. 使用大模型批改")
    print("2. 使用JSON比对")

    while True:
        choice = input("请输入选择（1或2）：").strip()
        if choice in ['1', '2']:
            return choice
        else:
            print("输入无效，请输入1或2")

def run_script(script_name, args=None):
    """运行指定的Python脚本"""
    try:
        print(f"\n正在运行 {script_name}...")
        print("=" * 50)

        # 构建命令
        cmd = [sys.executable, script_name]
        if args:
            cmd.extend(args)

        # 不捕获输出，让子进程直接与用户交互
        result = subprocess.run(cmd)

        print("=" * 50)
        if result.returncode == 0:
            print(f"✓ {script_name} 运行成功")
            return True
        else:
            print(f"✗ {script_name} 运行失败，返回码: {result.returncode}")
            return False

    except Exception as e:
        print(f"✗ 运行 {script_name} 时发生异常: {str(e)}")
        return False

def save_config(config_data):
    """保存配置到临时文件"""
    config_file = "temp_config.json"
    with open(config_file, 'w', encoding='utf-8') as f:
        json.dump(config_data, f, ensure_ascii=False, indent=2)
    return config_file

def load_config():
    """从临时文件加载配置"""
    config_file = "temp_config.json"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None

def cleanup_config():
    """清理临时配置文件"""
    config_file = "temp_config.json"
    if os.path.exists(config_file):
        try:
            os.remove(config_file)
        except:
            pass

def print_batch_summary(batch_results):
    """打印批处理执行总结"""
    print(f"\n{'='*80}")
    print("批处理执行总结")
    print(f"{'='*80}")

    total_batches = len(batch_results)
    successful_batches = [r for r in batch_results if r['success']]
    failed_batches = [r for r in batch_results if not r['success']]

    print(f"总批处理数量: {total_batches}")
    print(f"成功执行: {len(successful_batches)}")
    print(f"执行失败: {len(failed_batches)}")
    print(f"成功率: {len(successful_batches)/total_batches*100:.1f}%")

    if successful_batches:
        print(f"\n{'='*60}")
        print("✓ 成功执行的批处理:")
        print(f"{'='*60}")
        for result in successful_batches:
            config = result['config']
            print(f"\n第 {result['batch_index']} 个批处理:")
            print(f"  模式: {result['stage_mode']}")
            if result['stage_mode'] == '双阶段':
                print(f"  Round2: {result['round2_mode']}")
            print(f"  模型ID: {config.get('模型ID')}")
            print(f"  题型: {config.get('题型')}")
            print(f"  图像文件夹: {config.get('图像文件夹')}")
            print(f"  像素增强: {config.get('像素增强')}")
            print(f"  像素粘连: {config.get('像素粘连', 2)}")
            print(f"  放大倍数: {config.get('图像放大倍数')}")

            if result['output_files']:
                print(f"  生成的文档:")
                for output_file in result['output_files']:
                    print(f"    - {output_file['description']}: {output_file['directory']}")
                    # 尝试找到最新的md文件
                    try:
                        if os.path.exists(output_file['directory']):
                            md_files = [f for f in os.listdir(output_file['directory'])
                                      if f.endswith('.md') and not f.startswith('answer')]
                            if md_files:
                                md_files.sort(reverse=True)  # 按时间倒序
                                latest_md = md_files[0]
                                full_path = os.path.join(output_file['directory'], latest_md)
                                print(f"      最新文件: {full_path}")
                    except Exception as e:
                        print(f"      (无法检查文件: {e})")

    if failed_batches:
        print(f"\n{'='*60}")
        print("✗ 执行失败的批处理:")
        print(f"{'='*60}")
        for result in failed_batches:
            config = result['config']
            print(f"\n第 {result['batch_index']} 个批处理:")
            print(f"  模式: {result['stage_mode']}")
            if result['stage_mode'] == '双阶段':
                print(f"  Round2: {result['round2_mode']}")
            print(f"  模型ID: {config.get('模型ID')}")
            print(f"  题型: {config.get('题型')}")
            print(f"  图像文件夹: {config.get('图像文件夹')}")
            print(f"  像素增强: {config.get('像素增强')}")
            print(f"  像素粘连: {config.get('像素粘连', 2)}")
            print(f"  错误信息: {result.get('error_message', '未知错误')}")

    print(f"\n{'='*80}")
    print("批处理执行完成！")
    print(f"{'='*80}")

def run_single_batch_config(config, batch_index):
    """运行单个批处理配置，返回执行结果"""
    print(f"\n{'='*60}")
    print(f"开始执行第 {batch_index + 1} 个批处理配置")
    print(f"{'='*60}")

    # 从配置中提取参数（现在应该都有默认值了）
    # 处理新旧格式的兼容性
    processing_mode = config.get("处理模式", 1)

    # 如果存在旧格式的"round2图片"参数，则转换为新格式
    if "round2图片" in config:
        stage_choice = config.get("处理模式", 1)
        round2_choice = config.get("round2图片", 1)

        if stage_choice == 1:
            processing_mode = 1  # 单阶段
        elif stage_choice == 2:
            if round2_choice == 1:
                processing_mode = 3  # 双阶段发图片
            else:
                processing_mode = 2  # 双阶段不发图片
        print(f"检测到旧格式配置，已转换：处理模式={stage_choice}, round2图片={round2_choice} -> 新处理模式={processing_mode}")

    round2_grading_mode = str(config.get("round2批改模式", 2))  # 1=大模型批改，2=JSON比对
    model_id_num = config.get("模型ID", 1)
    response_format_num = config.get("response_format", 1)  # 1=text，2=json_object
    question_type_num = config.get("题型")  # 题型是必需字段，不设默认值
    images_dir_num = config.get("图像文件夹", 1)
    use_enhance = config.get("像素增强", "n") == "y"  # 默认值改为"n"
    use_pixel_connection = config.get("像素粘连", "n") == "y"  # "y"=采用，"n"=不采用，默认为"n"
    scale = config.get("图像放大倍数", 1)  # 默认值改为1

    # 处理灰度阀门参数
    # 如果像素增强为"n"，则直接忽略灰度阀门参数
    gray_threshold = None
    if use_enhance:  # 只有在像素增强为"y"时才考虑灰度阀门
        if "灰度阀门" in config:
            gray_threshold = config.get("灰度阀门")
            # 验证灰度阀门值是否在有效范围内
            if not isinstance(gray_threshold, (int, float)) or not (0 <= gray_threshold <= 255):
                print(f"警告：灰度阀门值 {gray_threshold} 无效，必须在0-255范围内，将使用默认值200")
                gray_threshold = 200
            else:
                print(f"使用batch_configs中的灰度阀门值: {gray_threshold}")
        else:
            # 如果没有灰度阀门参数，使用默认值200
            gray_threshold = 200
            print(f"未指定灰度阀门参数，使用默认值: {gray_threshold}")
    else:
        print("像素增强为'n'，忽略灰度阀门参数")

    # 提取自定义prompt参数
    # 支持两种格式：
    # 1. 直接字符串内容
    # 2. 以.md结尾的文件名，从batch_configs/prompt目录读取
    test_prompt = load_prompt_content(config.get("test_prompt"))
    test2_prompt = load_prompt_content(config.get("test2_prompt"))
    test3_prompt = load_prompt_content(config.get("test3_prompt"))
    one_stage_test_prompt = load_prompt_content(config.get("one_stage_test_prompt"))

    # 提取API参数（temperature、top_p、max_tokens）
    temperature = config.get("temperature")
    top_p = config.get("top_p")
    max_tokens = config.get("max_tokens")

    # 检查必需字段
    if question_type_num is None:
        print(f"错误：配置中缺少必需的'题型'字段")
        if processing_mode == 1:
            stage_mode = '单阶段'
            round2_mode = None
        elif processing_mode == 2:
            stage_mode = '双阶段'
            round2_mode = '不发图片'
        elif processing_mode == 3:
            stage_mode = '双阶段'
            round2_mode = '发图片'
        else:
            stage_mode = '未知'
            round2_mode = None

        result = {
            'batch_index': batch_index + 1,
            'config': config,
            'success': False,
            'stage_mode': stage_mode,
            'round2_mode': round2_mode,
            'output_files': [],
            'error_message': "配置中缺少必需的'题型'字段"
        }
        return result

    print(f"配置参数：")
    if processing_mode == 1:
        print(f"  处理模式: 单阶段处理")
    elif processing_mode == 2:
        print(f"  处理模式: 双阶段不发图片")
        print(f"  round2批改模式: {round2_grading_mode} ({'大模型批改' if round2_grading_mode == '1' else 'JSON比对'})")
    elif processing_mode == 3:
        print(f"  处理模式: 双阶段发图片")
    print(f"  模型ID: {model_id_num}")
    print(f"  response_format: {response_format_num} ({'text' if response_format_num == 1 else 'json_object'})")
    print(f"  题型: {question_type_num}")
    print(f"  图像文件夹: {images_dir_num}")
    print(f"  像素增强: {'是' if use_enhance else '否'}")
    if use_enhance and gray_threshold is not None:
        print(f"  灰度阀门: {gray_threshold}")
    print(f"  像素粘连: {'是' if use_pixel_connection else '否'}")
    print(f"  图像放大倍数: {scale}")

    # 初始化结果对象
    if processing_mode == 1:
        stage_mode = '单阶段'
        round2_mode = None
    elif processing_mode == 2:
        stage_mode = '双阶段'
        round2_mode = '不发图片'
    elif processing_mode == 3:
        stage_mode = '双阶段'
        round2_mode = '发图片'
    else:
        stage_mode = '未知'
        round2_mode = None

    result = {
        'batch_index': batch_index + 1,
        'config': config,
        'success': False,
        'stage_mode': stage_mode,
        'round2_mode': round2_mode,
        'output_files': [],
        'error_message': None
    }

    # 映射模型ID - 转换为模型字符串
    model_mapping = {
        1: "doubao-seed-1-6-250615",
        2: "doubao-seed-1-6-flash-250715",
        3: "doubao-1-5-thinking-vision-pro-250428",
        4: "doubao-1-5-vision-pro-32k-250115"
    }
    model_id = model_mapping.get(model_id_num, "doubao-seed-1-6-flash-250715")

    # 映射response_format - 转换为response_format字符串
    response_format_mapping = {
        1: "text",
        2: "json_object"
    }
    response_format = response_format_mapping.get(response_format_num, "text")

    # 映射题型 - 转换为题型字符串和拼音
    question_types = {
        1: "涂卡选择题",
        2: "涂卡判断题",
        3: "连线题",
        4: "图表题",
        5: "翻译题",
        6: "画图题",
        7: "数学应用题",
        8: "数学计算题",
        9: "简单的四则运算",
        10: "填空题",
        11: "判断题",
        12: "多选题",
        13: "单选题"
    }
    question_type = question_types.get(question_type_num, "涂卡选择题")
    pinyin_name = chinese_to_pinyin(question_type)

    # 映射图像文件夹 - 转换为文件夹名称
    folder_mapping = {
        1: "images",
        2: "OpenCV_result",
        3: "grounding_result",
        4: "YOLO_result",
        5: "YOLO_text_result",
        6: "manual_result",
        7: "roboflow_yolo_result"
    }
    images_dir_name = folder_mapping.get(images_dir_num, "images")

    if processing_mode == 1:
        # 单阶段模式
        print(f"\n执行单阶段模式")

        # 导入one_stage_test模块并调用其函数
        try:
            import one_stage_test
            success = one_stage_test.run_one_stage_test(
                model_id=model_id,
                response_format=response_format,
                question_type=question_type,
                pinyin_name=pinyin_name,
                images_dir=images_dir_name,
                use_enhance=use_enhance,
                scale=scale,
                use_pixel_connection=use_pixel_connection,
                custom_prompt=one_stage_test_prompt,
                temperature=temperature,
                top_p=top_p,
                max_tokens=max_tokens,
                gray_threshold=gray_threshold  # 传递灰度阀门参数
            )
            if success:
                print(f"✓ 第 {batch_index + 1} 个配置（单阶段）处理完成！")
                result['success'] = True
                # 添加输出文件路径
                types_dir = "types"
                question_dir = os.path.join(types_dir, pinyin_name)
                one_stage_response_dir = os.path.join(question_dir, "one_stage_response")
                result['output_files'].append({
                    'type': 'one_stage_response',
                    'directory': one_stage_response_dir,
                    'description': '单阶段处理结果'
                })
            else:
                print(f"✗ 第 {batch_index + 1} 个配置（单阶段）处理失败！")
                result['error_message'] = "单阶段处理返回失败状态"
        except Exception as e:
            print(f"✗ 第 {batch_index + 1} 个配置（单阶段）执行异常: {e}")
            result['error_message'] = str(e)

    elif processing_mode in [2, 3]:
        # 双阶段模式
        if processing_mode == 2:
            print(f"\n执行双阶段模式（不发图片）")
        else:  # processing_mode == 3
            print(f"\n执行双阶段模式（发图片）")

        try:
            import test

            # 运行test.py
            success1 = test.run_test(
                model_id=model_id,
                response_format=response_format,
                question_type=question_type,
                pinyin_name=pinyin_name,
                images_dir=images_dir_name,
                use_enhance=use_enhance,
                scale=scale,
                use_pixel_connection=use_pixel_connection,
                custom_prompt=test_prompt,
                temperature=temperature,
                top_p=top_p,
                max_tokens=max_tokens,
                gray_threshold=gray_threshold  # 传递灰度阀门参数
            )

            if not success1:
                print(f"✗ 第 {batch_index + 1} 个配置的test.py运行失败")
                result['error_message'] = "第一阶段test.py运行失败"
                return result

            # 保存配置供第二阶段使用
            types_dir = "types"
            question_dir = os.path.join(types_dir, pinyin_name)
            config_data = {
                'model_id': model_id,
                'response_format': response_format,
                'question_type': question_type,
                'pinyin_name': pinyin_name,
                'images_dir': images_dir_name,
                'question_dir': question_dir,
                'use_enhance': use_enhance,
                'scale': scale,
                'use_pixel_connection': use_pixel_connection,
                'grading_mode': 'model' if round2_grading_mode == '1' else 'json_compare',  # 添加批改模式
                'test2_prompt': test2_prompt,  # 添加test2自定义prompt
                'test3_prompt': test3_prompt,   # 添加test3自定义prompt
                'temperature': temperature,  # 添加temperature参数
                'top_p': top_p,  # 添加top_p参数
                'max_tokens': max_tokens,  # 添加max_tokens参数
                'gray_threshold': gray_threshold  # 添加灰度阀门参数
            }
            config_file = save_config(config_data)

            # 添加第一阶段输出文件
            response_dir = os.path.join(question_dir, "response")
            result['output_files'].append({
                'type': 'response',
                'directory': response_dir,
                'description': '第一阶段处理结果'
            })

            if processing_mode == 3:
                # 双阶段发图片：调用test3.py
                success2 = run_script("test3.py", ["--config", config_file])
                if success2:
                    print(f"✓ 第 {batch_index + 1} 个配置（双阶段-发图片）处理完成！")
                    result['success'] = True
                    # 添加第二阶段输出文件
                    round2_response_new_dir = os.path.join(question_dir, "round2_response_new")
                    result['output_files'].append({
                        'type': 'round2_response_new',
                        'directory': round2_response_new_dir,
                        'description': '第二阶段处理结果（发图片）'
                    })
                else:
                    print(f"✗ 第 {batch_index + 1} 个配置的test3.py运行失败")
                    result['error_message'] = "第二阶段test3.py运行失败"
            else:  # processing_mode == 2
                # 双阶段不发图片：调用test2.py
                success2 = run_script("test2.py", ["--config", config_file])
                if success2:
                    print(f"✓ 第 {batch_index + 1} 个配置（双阶段-不发图片）处理完成！")
                    result['success'] = True
                    # 添加第二阶段输出文件
                    round2_response_dir = os.path.join(question_dir, "round2_response")
                    result['output_files'].append({
                        'type': 'round2_response',
                        'directory': round2_response_dir,
                        'description': '第二阶段处理结果（不发图片）'
                    })
                else:
                    print(f"✗ 第 {batch_index + 1} 个配置的test2.py运行失败")
                    result['error_message'] = "第二阶段test2.py运行失败"

            # 清理配置文件
            cleanup_config()

        except Exception as e:
            print(f"✗ 第 {batch_index + 1} 个配置（双阶段）执行异常: {e}")
            result['error_message'] = str(e)
            cleanup_config()

    print(f"第 {batch_index + 1} 个批处理配置执行完成\n")
    return result

def main():
    """主函数：控制整个流程"""
    print("=== AI图像识别验证工具 ===")
    print("欢迎使用AI图像识别验证工具！")

    # 解析命令行参数
    args = parse_command_line_args()

    # 处理列出配置文件的命令
    if args.list:
        print("\n=== 可用的配置文件 ===")
        available_configs = list_available_configs()
        if available_configs:
            for i, config_file in enumerate(available_configs, 1):
                print(f"  {i}. {config_file}")
            print(f"\n总共找到 {len(available_configs)} 个配置文件")
        else:
            print("没有找到任何配置文件")
        return

    # 处理重命名命令
    if args.rename:
        print("\n=== 重命名配置文件 ===")
        available_configs = list_available_configs()
        if available_configs:
            print("重命名前的配置文件:")
            for i, config_file in enumerate(available_configs, 1):
                print(f"  {i}. {config_file}")

            non_numeric_files = [f for f in available_configs if not f[:-5].isdigit()]
            if non_numeric_files:
                print(f"\n检测到 {len(non_numeric_files)} 个非数字命名的配置文件")
                print("开始重命名...")
                if rename_batch_config_files():
                    print("\n重命名后的配置文件:")
                    new_configs = list_available_configs()
                    for i, config_file in enumerate(new_configs, 1):
                        print(f"  {i}. {config_file}")
                    print("\n✓ 重命名操作完成！")
                else:
                    print("✗ 重命名操作失败！")
            else:
                print("\n所有配置文件已经是数字格式，无需重命名")
        else:
            print("没有找到任何配置文件")
        return

    if args.name:
        # 命令行指定了配置文件
        print(f"\n使用命令行指定的配置文件: {args.name}")

        # 加载指定的批处理配置文件
        batch_data = get_batch_config(args.name)
        if batch_data is None:
            print("无法加载指定的批处理配置，程序退出")
            return

        batch_configs = batch_data.get("batch_configs", [])
        if not batch_configs:
            print("批处理配置文件中没有找到配置项，程序退出")
            return

        print(f"找到 {len(batch_configs)} 个批处理配置，开始依次执行...")

        # 依次执行每个批处理配置，收集结果
        batch_results = []
        for i, config in enumerate(batch_configs):
            result = run_single_batch_config(config, i)
            batch_results.append(result)

        # 输出批处理执行总结
        print_batch_summary(batch_results)
        return

    # 交互式模式
    print("\n进入交互式模式")

    # 检查是否需要重命名文件
    available_configs = list_available_configs()
    if available_configs:
        print(f"\n当前可用的配置文件:")
        for i, config_file in enumerate(available_configs, 1):
            print(f"  {i}. {config_file}")

        # 检查是否有非数字命名的文件
        non_numeric_files = [f for f in available_configs if not f[:-5].isdigit()]
        if non_numeric_files:
            print(f"\n检测到 {len(non_numeric_files)} 个非数字命名的配置文件")
            rename_choice = input("是否要将所有配置文件重命名为数字格式？(y/n): ").strip().lower()
            if rename_choice in ['y', 'yes']:
                if rename_batch_config_files():
                    print("文件重命名完成，请重新运行程序")
                    return
                else:
                    print("文件重命名失败")

    # 第一步：选择输入模式
    input_mode = get_input_mode_choice()

    if input_mode == '2':
        # batch_configs读取模式
        print("\n您选择了batch_configs读取模式")

        # 加载批处理配置文件（按数字排序的最小文件）
        batch_data = get_batch_config()
        if batch_data is None:
            print("无法加载批处理配置，程序退出")
            return

        batch_configs = batch_data.get("batch_configs", [])
        if not batch_configs:
            print("批处理配置文件中没有找到配置项，程序退出")
            return

        print(f"找到 {len(batch_configs)} 个批处理配置，开始依次执行...")

        # 依次执行每个批处理配置，收集结果
        batch_results = []
        for i, config in enumerate(batch_configs):
            result = run_single_batch_config(config, i)
            batch_results.append(result)

        # 输出批处理执行总结
        print_batch_summary(batch_results)
        return

    # 手动输入模式（原有逻辑）
    print("\n您选择了手动输入模式")

    # 第二步：选择处理模式
    processing_choice = get_processing_mode_choice()

    if processing_choice == '1':
        # 单阶段：直接调用one_stage_test脚本
        print("\n您选择了单阶段处理模式")
        success = run_script("one_stage_test.py")
        if success:
            print("\n✓ 单阶段处理完成！")
        else:
            print("\n✗ 单阶段处理失败！")

    elif processing_choice == '2':
        # 双阶段不发图片：先调用test再调用test2
        print("\n您选择了双阶段不发图片模式")

        # 询问round2批改模式
        round2_grading_choice = get_round2_grading_mode()
        grading_mode = 'model' if round2_grading_choice == '1' else 'json_compare'
        print(f"您选择了{'大模型批改' if round2_grading_choice == '1' else 'JSON比对'}模式")

        print("将依次运行：test.py -> test2.py")

        # 运行test.py并传递双阶段标志
        success1 = run_script("test.py", ["--stage", "dual"])
        if not success1:
            print("\n✗ test.py 运行失败，停止后续流程")
            cleanup_config()
            return

        # 加载test.py保存的配置
        config = load_config()
        if not config:
            print("\n✗ 无法加载配置信息，停止后续流程")
            return

        # 添加批改模式到配置中
        config['grading_mode'] = grading_mode

        # 重新保存配置
        config_file = save_config(config)

        # 运行test2.py并传递配置
        success2 = run_script("test2.py", ["--config", config_file])
        if success2:
            print("\n✓ 双阶段处理完成（不发图片）！")
        else:
            print("\n✗ test2.py 运行失败！")

        # 清理配置文件
        cleanup_config()

    elif processing_choice == '3':
        # 双阶段发图片：先调用test再调用test3
        print("\n您选择了双阶段发图片模式")
        print("将依次运行：test.py -> test3.py")

        # 运行test.py并传递双阶段标志
        success1 = run_script("test.py", ["--stage", "dual"])
        if not success1:
            print("\n✗ test.py 运行失败，停止后续流程")
            cleanup_config()
            return

        # 加载test.py保存的配置
        config = load_config()
        if not config:
            print("\n✗ 无法加载配置信息，停止后续流程")
            return

        # 运行test3.py并传递配置
        success3 = run_script("test3.py", ["--config", "temp_config.json"])
        if success3:
            print("\n✓ 双阶段处理完成（发图片）！")
        else:
            print("\n✗ test3.py 运行失败！")

        # 清理配置文件
        cleanup_config()
    
    print("\n程序运行结束。")

if __name__ == "__main__":
    main()
